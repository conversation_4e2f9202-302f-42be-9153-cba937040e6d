//@version=6
indicator(title="RSI & EMA Combined", shorttitle="RSI+EMA", timeframe="", timeframe_gaps=true)

// RSI Settings
rsiLengthInput = input.int(14, minval=1, title="RSI Length", group="RSI Settings")
rsiSourceInput = input.source(close, "Source", group="RSI Settings")
calculateDivergence = input.bool(false, title="Calculate Divergence", group="RSI Settings", display = display.data_window, tooltip = "Calculating divergences is needed in order for divergence alerts to fire.")

// EMA Settings
ema5Length = input.int(5, minval=1, title="EMA 5 Length", group="EMA Settings")
ema20Length = input.int(20, minval=1, title="EMA 20 Length", group="EMA Settings")
emaSource = input(close, title="EMA Source", group="EMA Settings")
emaOffset = input.int(title="EMA Offset", defval=0, minval=-500, maxval=500, group="EMA Settings", display = display.data_window)

// RSI Calculation
change = ta.change(rsiSourceInput)
up = ta.rma(math.max(change, 0), rsiLengthInput)
down = ta.rma(-math.min(change, 0), rsiLengthInput)
rsi = down == 0 ? 100 : up == 0 ? 0 : 100 - (100 / (1 + up / down))

// EMA Calculations
ema5 = ta.ema(emaSource, ema5Length)
ema20 = ta.ema(emaSource, ema20Length)

// RSI Plots (in separate pane)
rsiPlot = plot(rsi, "RSI", color=#7E57C2)
rsiUpperBand = hline(70, "RSI Upper Band", color=#787B86)
midline = hline(50, "RSI Middle Band", color=color.new(#787B86, 50))
rsiLowerBand = hline(30, "RSI Lower Band", color=#787B86)
fill(rsiUpperBand, rsiLowerBand, color=color.rgb(126, 87, 194, 90), title="RSI Background Fill")
midLinePlot = plot(50, color = na, editable = false, display = display.none)
fill(rsiPlot, midLinePlot, 100, 70, top_color = color.new(color.green, 0), bottom_color = color.new(color.green, 100),  title = "Overbought Gradient Fill")
fill(rsiPlot, midLinePlot, 30,  0,  top_color = color.new(color.red, 100), bottom_color = color.new(color.red, 0),      title = "Oversold Gradient Fill")

// RSI Smoothing MA inputs
rsiSmoothingGroup = "RSI Smoothing"
rsiTT_BB = "Only applies when 'SMA + Bollinger Bands' is selected. Determines the distance between the SMA and the bands."
rsiMaTypeInput = input.string("None", "RSI Smoothing Type", options = ["None", "SMA", "SMA + Bollinger Bands", "EMA", "SMMA (RMA)", "WMA", "VWMA"], group = rsiSmoothingGroup, display = display.data_window)
rsiMaLengthInput = input.int(14, "RSI Smoothing Length", group = rsiSmoothingGroup, display = display.data_window)
rsiBbMultInput = input.float(2.0, "RSI BB StdDev", minval = 0.001, maxval = 50, step = 0.5, tooltip = rsiTT_BB, group = rsiSmoothingGroup, display = display.data_window)
var rsiEnableMA = rsiMaTypeInput != "None"
var rsiIsBB = rsiMaTypeInput == "SMA + Bollinger Bands"

// RSI Smoothing MA Calculation
rsiMa(source, length, MAtype) =>
	switch MAtype
		"SMA"                   => ta.sma(source, length)
		"SMA + Bollinger Bands" => ta.sma(source, length)
		"EMA"                   => ta.ema(source, length)
		"SMMA (RMA)"            => ta.rma(source, length)
		"WMA"                   => ta.wma(source, length)
		"VWMA"                  => ta.vwma(source, length)

// RSI Smoothing MA plots
rsiSmoothingMA = rsiEnableMA ? rsiMa(rsi, rsiMaLengthInput, rsiMaTypeInput) : na
rsiSmoothingStDev = rsiIsBB ? ta.stdev(rsi, rsiMaLengthInput) * rsiBbMultInput : na
plot(rsiSmoothingMA, "RSI-based MA", color=color.yellow, display = rsiEnableMA ? display.all : display.none, editable = rsiEnableMA)
rsiBbUpperBand = plot(rsiSmoothingMA + rsiSmoothingStDev, title = "RSI Upper Bollinger Band", color=color.green, display = rsiIsBB ? display.all : display.none, editable = rsiIsBB)
rsiBbLowerBand = plot(rsiSmoothingMA - rsiSmoothingStDev, title = "RSI Lower Bollinger Band", color=color.green, display = rsiIsBB ? display.all : display.none, editable = rsiIsBB)
fill(rsiBbUpperBand, rsiBbLowerBand, color= rsiIsBB ? color.new(color.green, 90) : na, title="RSI Bollinger Bands Background Fill", display = rsiIsBB ? display.all : display.none, editable = rsiIsBB)

// RSI Divergence
lookbackRight = 5
lookbackLeft = 5
rangeUpper = 60
rangeLower = 5
bearColor = color.red
bullColor = color.green
textColor = color.white
noneColor = color.new(color.white, 100)

_inRange(bool cond) =>
    bars = ta.barssince(cond)
    rangeLower <= bars and bars <= rangeUpper

plFound = false
phFound = false

bullCond = false
bearCond = false

rsiLBR = rsi[lookbackRight]

if calculateDivergence
    //------------------------------------------------------------------------------
    // Regular Bullish
    // rsi: Higher Low
    plFound := not na(ta.pivotlow(rsi, lookbackLeft, lookbackRight))
    rsiHL = rsiLBR > ta.valuewhen(plFound, rsiLBR, 1) and _inRange(plFound[1])
    // Price: Lower Low
    lowLBR = low[lookbackRight]
    priceLL = lowLBR < ta.valuewhen(plFound, lowLBR, 1)
    bullCond := priceLL and rsiHL and plFound

    //------------------------------------------------------------------------------
    // Regular Bearish
    // rsi: Lower High
    phFound := not na(ta.pivothigh(rsi, lookbackLeft, lookbackRight))
    rsiLH = rsiLBR < ta.valuewhen(phFound, rsiLBR, 1) and _inRange(phFound[1])
    // Price: Higher High
    highLBR = high[lookbackRight]
    priceHH = highLBR > ta.valuewhen(phFound, highLBR, 1)
    bearCond := priceHH and rsiLH and phFound

plot(
     plFound   ? rsiLBR : na,
     offset    = -lookbackRight,
     title     = "Regular Bullish",
     linewidth = 2,
     color     = (bullCond ? bullColor : noneColor),
     display   = display.pane,
     editable  = calculateDivergence)

plotshape(
     bullCond  ? rsiLBR : na,
     offset    = -lookbackRight,
     title     = "Regular Bullish Label",
     text      = " Bull ",
     style     = shape.labelup,
     location  = location.absolute,
     color     = bullColor,
     textcolor = textColor,
     display   = display.pane,
     editable  = calculateDivergence)

plot(
     phFound   ? rsiLBR : na,
     offset    = -lookbackRight,
     title     = "Regular Bearish",
     linewidth = 2,
     color     = (bearCond ? bearColor : noneColor),
     display   = display.pane,
     editable  = calculateDivergence)

plotshape(
     bearCond  ? rsiLBR : na,
     offset    = -lookbackRight,
     title     = "Regular Bearish Label",
     text      = " Bear ",
     style     = shape.labeldown,
     location  = location.absolute,
     color     = bearColor,
     textcolor = textColor,
     display   = display.pane,
     editable  = calculateDivergence)

alertcondition(bullCond, title='Regular Bullish Divergence', message="Found a new Regular Bullish Divergence, `Pivot Lookback Right` number of bars to the left of the current bar.")
alertcondition(bearCond, title='Regular Bearish Divergence', message='Found a new Regular Bearish Divergence, `Pivot Lookback Right` number of bars to the left of the current bar.')

// EMA Plots (on main chart)
plot(ema5, title="EMA 5", color=color.blue, offset=emaOffset, linewidth=2)
plot(ema20, title="EMA 20", color=color.orange, offset=emaOffset, linewidth=2)

// EMA 5 Smoothing MA inputs
ema5SmoothingGroup = "EMA 5 Smoothing"
ema5TT_BB = "Only applies when 'SMA + Bollinger Bands' is selected. Determines the distance between the SMA and the bands."
ema5MaTypeInput = input.string("None", "EMA 5 Smoothing Type", options = ["None", "SMA", "SMA + Bollinger Bands", "EMA", "SMMA (RMA)", "WMA", "VWMA"], group = ema5SmoothingGroup, display = display.data_window)
ema5MaLengthInput = input.int(14, "EMA 5 Smoothing Length", group = ema5SmoothingGroup, display = display.data_window)
ema5BbMultInput = input.float(2.0, "EMA 5 BB StdDev", minval = 0.001, maxval = 50, step = 0.5, tooltip = ema5TT_BB, group = ema5SmoothingGroup, display = display.data_window)
var ema5EnableMA = ema5MaTypeInput != "None"
var ema5IsBB = ema5MaTypeInput == "SMA + Bollinger Bands"

// EMA 20 Smoothing MA inputs
ema20SmoothingGroup = "EMA 20 Smoothing"
ema20TT_BB = "Only applies when 'SMA + Bollinger Bands' is selected. Determines the distance between the SMA and the bands."
ema20MaTypeInput = input.string("None", "EMA 20 Smoothing Type", options = ["None", "SMA", "SMA + Bollinger Bands", "EMA", "SMMA (RMA)", "WMA", "VWMA"], group = ema20SmoothingGroup, display = display.data_window)
ema20MaLengthInput = input.int(14, "EMA 20 Smoothing Length", group = ema20SmoothingGroup, display = display.data_window)
ema20BbMultInput = input.float(2.0, "EMA 20 BB StdDev", minval = 0.001, maxval = 50, step = 0.5, tooltip = ema20TT_BB, group = ema20SmoothingGroup, display = display.data_window)
var ema20EnableMA = ema20MaTypeInput != "None"
var ema20IsBB = ema20MaTypeInput == "SMA + Bollinger Bands"

// EMA Smoothing MA Calculation
emaMa(source, length, MAtype) =>
	switch MAtype
		"SMA"                   => ta.sma(source, length)
		"SMA + Bollinger Bands" => ta.sma(source, length)
		"EMA"                   => ta.ema(source, length)
		"SMMA (RMA)"            => ta.rma(source, length)
		"WMA"                   => ta.wma(source, length)
		"VWMA"                  => ta.vwma(source, length)

// EMA 5 Smoothing MA plots
ema5SmoothingMA = ema5EnableMA ? emaMa(ema5, ema5MaLengthInput, ema5MaTypeInput) : na
ema5SmoothingStDev = ema5IsBB ? ta.stdev(ema5, ema5MaLengthInput) * ema5BbMultInput : na
plot(ema5SmoothingMA, "EMA 5-based MA", color=color.yellow, display = ema5EnableMA ? display.all : display.none, editable = ema5EnableMA)
ema5BbUpperBand = plot(ema5SmoothingMA + ema5SmoothingStDev, title = "EMA 5 Upper Bollinger Band", color=color.green, display = ema5IsBB ? display.all : display.none, editable = ema5IsBB)
ema5BbLowerBand = plot(ema5SmoothingMA - ema5SmoothingStDev, title = "EMA 5 Lower Bollinger Band", color=color.green, display = ema5IsBB ? display.all : display.none, editable = ema5IsBB)
fill(ema5BbUpperBand, ema5BbLowerBand, color= ema5IsBB ? color.new(color.green, 90) : na, title="EMA 5 Bollinger Bands Background Fill", display = ema5IsBB ? display.all : display.none, editable = ema5IsBB)

// EMA 20 Smoothing MA plots
ema20SmoothingMA = ema20EnableMA ? emaMa(ema20, ema20MaLengthInput, ema20MaTypeInput) : na
ema20SmoothingStDev = ema20IsBB ? ta.stdev(ema20, ema20MaLengthInput) * ema20BbMultInput : na
plot(ema20SmoothingMA, "EMA 20-based MA", color=color.purple, display = ema20EnableMA ? display.all : display.none, editable = ema20EnableMA)
ema20BbUpperBand = plot(ema20SmoothingMA + ema20SmoothingStDev, title = "EMA 20 Upper Bollinger Band", color=color.red, display = ema20IsBB ? display.all : display.none, editable = ema20IsBB)
ema20BbLowerBand = plot(ema20SmoothingMA - ema20SmoothingStDev, title = "EMA 20 Lower Bollinger Band", color=color.red, display = ema20IsBB ? display.all : display.none, editable = ema20IsBB)
fill(ema20BbUpperBand, ema20BbLowerBand, color= ema20IsBB ? color.new(color.red, 90) : na, title="EMA 20 Bollinger Bands Background Fill", display = ema20IsBB ? display.all : display.none, editable = ema20IsBB)
